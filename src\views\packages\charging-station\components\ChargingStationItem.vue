<template>
  <div class="charge-card">
    <!-- 标题和距离 -->
    <div class="card-header">
      <div class="title">
        <van-icon name="star" color="#ff7e00" />
        <span class="name">{{ station.name }}</span>
      </div>
      <div class="distance">{{ distance ? distance + 'km' : '--' }}</div>
    </div>

    <!-- 评分和功率 -->
    <div class="card-subtitle">
      <span class="score">{{station.rating}}</span>
      <span class="desc">地下B1 | 最快360kW | 直营</span>
    </div>

    <!-- 价格信息 -->
    <div class="card-price">
      <span class="now">¥0.9055</span>
      <span class="vip">VIP ¥0.8573</span>
      <span class="old">¥1.09 8.2折</span>
    </div>

    <!-- 优惠券 -->
    <div class="card-coupon">
      <van-tag type="danger">券</van-tag>
      <span class="coupon-text">兑换满5减3</span>
    </div>

    <!-- 停车信息 -->
    <div class="card-footer">
      <div class="parking">
        <van-icon name="passed" color="#1989fa" />
        <span>限时免费: 进入充电站内部通道充电，免费停…</span>
      </div>
      <div class="fast">
        <van-tag color="#ff7e00" text-color="#fff">快</van-tag>
        <span class="count">18/30</span>
      </div>
    </div>
  </div>
</template>

<script>
import { formatDate, getDistance } from '@/utils';
import { isValidCoordinate } from '../utils';
import { Icon, Tag, Popup, Button } from 'vant';
export default {
  name: 'ChargeCard',
  components: {
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [Popup.name]: Popup,
    [Button.name]: Button,
  },
  props: {
    station: {
      type: Object,
      required: true,
    },
    geo: {
      type: Object,
      required: true,
    },
  },
  computed: {
    distance() {
      // 如果station已有distance属性，直接返回
      if (this.station.distance) {
        return this.station.distance;
      }

      // 验证坐标有效性
      const stationLat = this.station.latitude;
      const stationLng = this.station.longitude;
      const geoLat = this.geo.latitude;
      const geoLng = this.geo.longitude;

      // 使用项目现有的坐标验证函数
      if (
        !isValidCoordinate(stationLat, stationLng) ||
        !isValidCoordinate(geoLat, geoLng)
      ) {
        return '--'; // 坐标无效时返回默认显示值
      }

      let _distance = getDistance(stationLat, stationLng, geoLat, geoLng);
      return _distance.toFixed(0); // 直线距离
    },
  },
};
</script>

<style lang="scss" scoped>
.charge-card {
  background: #fff;
  border-radius: 12px;
  padding: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin: 0px 0 12px;
  font-size: 14px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
      display: flex;
      align-items: center;

      .name {
        margin-left: 6px;
        font-weight: 600;
      }
    }

    .distance {
      color: #666;
      font-size: 12px;
    }
  }

  .card-subtitle {
    margin-top: 6px;
    font-size: 12px;
    color: #999;

    .score {
      color: #ff7e00;
      font-weight: bold;
      margin-right: 6px;
    }
  }

  .card-price {
    margin: 8px 0;

    .now {
      font-size: 16px;
      font-weight: bold;
      color: #000;
      margin-right: 8px;
    }

    .vip {
      background: #fff2e5;
      color: #ff7e00;
      padding: 2px 6px;
      border-radius: 4px;
      margin-right: 8px;
    }

    .old {
      font-size: 12px;
      color: #999;
      text-decoration: line-through;
    }
  }

  .card-coupon {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .coupon-text {
      margin-left: 4px;
      color: #ff4d4f;
      font-size: 13px;
    }
  }

  .card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #666;

    .parking {
      display: flex;
      align-items: center;

      span {
        margin-left: 4px;
      }
    }

    .fast {
      display: flex;
      align-items: center;

      .count {
        margin-left: 4px;
        color: #333;
      }
    }
  }
}
</style>
